# Python虚拟环境配置说明

## 环境概述

本项目已成功配置了一个完整的Python虚拟环境，包含了运行所有Python脚本所需的依赖包。

### 环境信息
- **Python版本**: 3.9.13
- **虚拟环境位置**: `./venv/`
- **CUDA支持**: 是 (CUDA 11.8)
- **PyTorch版本**: 2.7.1+cu118

## 已安装的主要依赖包

### 科学计算和数据处理
- **NumPy** 2.0.2 - 数值计算库
- **Pandas** 2.3.1 - 数据处理和分析
- **SciPy** 1.13.1 - 科学计算库

### 机器学习
- **Scikit-learn** 1.6.1 - 机器学习库
- **PyTorch** 2.7.1+cu118 - 深度学习框架（支持CUDA）
- **GPyTorch** 1.13 - 高斯过程库

### 数据可视化
- **Matplotlib** 3.9.4 - 基础绘图库
- **Seaborn** 0.13.2 - 统计绘图库

### 项目模块
- **utilts.py** - 工具函数模块
- **pytorch_lnn_ts.py** - PyTorch LNN时间序列模块
- **model_script.py** - 模型脚本模块

## 如何使用虚拟环境

### 1. 激活虚拟环境

#### Windows PowerShell (推荐)
```powershell
# 在项目根目录下执行
.\venv\Scripts\Activate.ps1
```

#### Windows Command Prompt
```cmd
# 在项目根目录下执行
.\venv\Scripts\activate.bat
```

#### 直接使用虚拟环境中的Python
```powershell
# 不需要激活，直接使用虚拟环境中的Python
.\venv\Scripts\python.exe your_script.py
```

### 2. 运行Python脚本

激活虚拟环境后，可以正常运行Python脚本：

```powershell
# 激活环境后
python model_script.py
python pytorch_lnn_ts.py
python utilts.py

# 或者运行Jupyter Notebook
python -m jupyter notebook "main函数-协变量处理-最终版本-跑这个就行_v2.ipynb"
```

### 3. 直接使用（无需激活）

```powershell
# 直接使用虚拟环境中的Python运行脚本
.\venv\Scripts\python.exe model_script.py
.\venv\Scripts\python.exe pytorch_lnn_ts.py
.\venv\Scripts\python.exe utilts.py
```

### 4. 验证环境配置

运行环境测试脚本来验证所有依赖是否正确安装：

```powershell
.\venv\Scripts\python.exe test_environment.py
```

## 添加新的依赖包

如果需要安装新的Python包，请使用以下方法：

### 方法1：激活环境后安装
```powershell
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 安装新包
pip install package_name

# 更新requirements文件
pip freeze > requirements_clean.txt
```

### 方法2：直接使用虚拟环境的pip
```powershell
# 直接使用虚拟环境中的pip
.\venv\Scripts\pip.exe install package_name

# 更新requirements文件
.\venv\Scripts\pip.exe freeze > requirements_clean.txt
```

## 在其他机器上重建环境

如果需要在其他机器上重建相同的环境：

```powershell
# 1. 创建新的虚拟环境
python -m venv venv

# 2. 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 3. 安装依赖包
pip install -r requirements_clean.txt
```

## 注意事项

1. **CUDA支持**: 当前环境配置了CUDA 11.8支持的PyTorch。如果您的系统没有CUDA或使用不同版本的CUDA，可能需要重新安装PyTorch。

2. **PowerShell执行策略**: 如果遇到PowerShell执行策略限制，可以：
   - 使用管理员权限运行PowerShell
   - 或者直接使用 `.\venv\Scripts\python.exe` 运行脚本

3. **编码问题**: 如果遇到中文编码问题，建议在脚本开头添加：
   ```python
   # -*- coding: utf-8 -*-
   ```

4. **内存使用**: 深度学习模型可能需要大量内存，特别是使用GPU时。

## 故障排除

### 常见问题及解决方案

1. **模块导入错误**
   ```
   解决方案：确保在项目根目录下运行脚本，或将项目目录添加到Python路径
   ```

2. **CUDA不可用**
   ```
   解决方案：检查NVIDIA驱动和CUDA安装，或安装CPU版本的PyTorch
   ```

3. **内存不足**
   ```
   解决方案：减少批处理大小或使用更小的模型
   ```

## 文件说明

- `venv/` - 虚拟环境目录
- `requirements_clean.txt` - 依赖包列表
- `test_environment.py` - 环境测试脚本
- `环境配置说明.md` - 本说明文档

## 联系支持

如果遇到环境配置问题，请检查：
1. Python版本是否为3.9.13
2. 所有依赖包是否正确安装
3. CUDA驱动是否正确安装（如果使用GPU）

---
*环境配置完成时间: 2025年1月*
