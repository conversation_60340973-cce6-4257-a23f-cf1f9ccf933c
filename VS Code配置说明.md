# VS Code 虚拟环境配置说明

## 🎯 目标
让VS Code自动使用项目的虚拟环境运行所有Python脚本。

## 📁 已创建的配置文件

我已经为您创建了以下配置文件：

### 1. `.vscode/settings.json` - VS Code工作区设置
- 指定Python解释器路径为虚拟环境
- 启用终端自动激活环境
- 配置代码分析和格式化

### 2. `.vscode/launch.json` - 调试配置
- 为每个Python脚本创建了专门的调试配置
- 所有配置都使用虚拟环境的Python解释器

### 3. `.env` - 环境变量文件
- 设置PYTHONPATH
- 配置CUDA设备

## 🔧 配置步骤

### 步骤1：重启VS Code
```
关闭VS Code，然后重新打开项目文件夹
```

### 步骤2：验证Python解释器
1. 打开任意Python文件
2. 查看VS Code底部状态栏，应该显示：
   ```
   Python 3.9.13 ('venv': venv) ./venv/Scripts/python.exe
   ```

### 步骤3：手动选择解释器（如果需要）
如果状态栏显示的不是虚拟环境：

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `Python: Select Interpreter`
3. 选择：`./venv/Scripts/python.exe`

## 🚀 使用方法

### 方法1：直接运行（F5）
1. 打开任意Python文件（如 `model_script.py`）
2. 按 `F5` 或点击"运行和调试"
3. 选择对应的配置（如"Python: model_script.py"）

### 方法2：右键运行
1. 在Python文件中右键
2. 选择"在终端中运行Python文件"
3. 会自动使用虚拟环境

### 方法3：终端运行
1. 在VS Code中打开终端 (`Ctrl+``)
2. 终端会自动激活虚拟环境
3. 直接运行：`python your_script.py`

## 📋 可用的调试配置

我为您创建了以下调试配置：

1. **Python: Current File** - 运行当前打开的文件
2. **Python: model_script.py** - 运行模型脚本
3. **Python: pytorch_lnn_ts.py** - 运行PyTorch LNN时间序列脚本
4. **Python: utilts.py** - 运行工具函数脚本
5. **Python: test_environment.py** - 运行环境测试脚本

## ✅ 验证配置是否成功

### 检查1：状态栏显示
VS Code底部应该显示：
```
Python 3.9.13 ('venv': venv) ./venv/Scripts/python.exe
```

### 检查2：运行测试脚本
1. 打开 `test_environment.py`
2. 按 `F5` 运行
3. 应该看到所有测试通过的输出

### 检查3：终端环境
1. 在VS Code中打开新终端
2. 应该看到提示符前有 `(venv)` 标识
3. 运行 `python --version` 应该显示 `Python 3.9.13`

## 🔍 故障排除

### 问题1：VS Code仍使用系统Python
**解决方案：**
1. 删除 `.vscode/settings.json` 中的缓存
2. 重启VS Code
3. 手动选择解释器

### 问题2：终端没有自动激活虚拟环境
**解决方案：**
1. 检查 `settings.json` 中的配置
2. 在终端中手动运行：
   ```powershell
   .\venv\Scripts\Activate.ps1
   ```

### 问题3：模块导入错误
**解决方案：**
1. 确保 `.env` 文件中设置了 `PYTHONPATH=.`
2. 检查工作目录是否正确

### 问题4：CUDA不可用
**解决方案：**
1. 检查 `.env` 文件中的 `CUDA_VISIBLE_DEVICES=0`
2. 运行环境测试脚本验证CUDA状态

## 📝 配置文件说明

### `.vscode/settings.json` 关键配置
```json
{
    "python.pythonPath": "./venv/Scripts/python.exe",
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true
}
```

### `.vscode/launch.json` 关键配置
```json
{
    "python": "${workspaceFolder}/venv/Scripts/python.exe",
    "cwd": "${workspaceFolder}"
}
```

## 🎉 完成！

现在您可以：
- ✅ 直接按F5运行Python脚本
- ✅ 右键"在终端中运行Python文件"
- ✅ 在集成终端中直接使用python命令
- ✅ 使用调试功能
- ✅ 享受代码补全和智能提示

所有操作都会自动使用您的虚拟环境！
