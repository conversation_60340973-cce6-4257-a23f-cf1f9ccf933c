{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: model_script.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/model_script.py", "console": "integratedTerminal", "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}"}, {"name": "Python: pytorch_lnn_ts.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/pytorch_lnn_ts.py", "console": "integratedTerminal", "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}"}, {"name": "Python: utilts.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/utilts.py", "console": "integratedTerminal", "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}"}, {"name": "Python: test_environment.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/test_environment.py", "console": "integratedTerminal", "python": "${workspaceFolder}/venv/Scripts/python.exe", "cwd": "${workspaceFolder}"}]}