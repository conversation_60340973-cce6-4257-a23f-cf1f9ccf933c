{"python.pythonPath": "./venv/Scripts/python.exe", "python.defaultInterpreterPath": "./venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.envFile": "${workspaceFolder}/.env", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "files.associations": {"*.py": "python"}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}"}}